@import "../../../../theme/variables.scss";

.update-container {
  display: flex;
  flex-direction: column;
  height: 100vh; // 使用视口高度
  max-height: 100vh;
  overflow: hidden;
}

.check-update-header {
  text-align: center;
  min-height: 42px; // 减少最小高度，使其更紧凑
  // padding: 10px 16px; // 减少内边距
  // line-height: 1.3; // 调整行高
  width: 100%;
  color: var(--ion-color-primary-contrast);
  background-color: var(--ion-color-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  font-weight: 500;
  flex-shrink: 0; // 防止header被压缩
}

.check-update-context {
  display: flex;
  flex-direction: column;
  justify-content: flex-start; // 改为顶部对齐，避免内容被遮挡
  align-items: flex-start; // 左对齐
  flex: 1; // 使用flex: 1而不是固定高度
  white-space: pre-line;
  overflow-y: auto;
  padding: 16px; // 增加内边距
  line-height: 1.6; // 增加行高提高可读性
  font-size: 14px;
  color: #333;
  word-wrap: break-word; // 确保长文本换行
  word-break: break-word;
  min-height: 0; // 确保flex子元素可以正确收缩
}

.check-update-footer {
  border-top: 1px solid #f6f6f6;
  min-height: 42px; // 减少最小高度，使其更紧凑
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  // padding: 10px 16px; // 减少内边距
  background-color: #fff;
  flex-shrink: 0; // 防止footer被压缩
}

.btn-confirm {
  width: 49%;
  text-align: center;
  color: var(--ion-color-primary);
  padding: 8px 8px; // 减少按钮内边距，使其更紧凑
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  border-radius: 4px;
  transition: background-color 0.2s;
  
  &:hover {
    background-color: rgba(var(--ion-color-primary-rgb), 0.1);
  }
}

.btn-cancal {
  width: 49%;
  text-align: center;
  color: #666;
  padding: 8px 8px; // 减少按钮内边距，使其更紧凑
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  border-radius: 4px;
  transition: background-color 0.2s;
  
  &:hover {
    background-color: rgba(0, 0, 0, 0.05);
  }
}

// 添加响应式适配
@media (max-height: 600px) {
  .check-update-header {
    min-height: 42px; // 进一步减少高度
    padding: 8px 16px;
    font-size: 15px;
  }

  .check-update-context {
    padding: 12px;
    font-size: 13px;
  }

  .check-update-footer {
    min-height: 42px; // 进一步减少高度
    padding: 8px 16px;
  }
}

@media (max-height: 500px) {
  .check-update-header {
    min-height: 36px; // 进一步减少高度
    padding: 6px 16px;
    font-size: 14px;
  }

  .check-update-context {
    padding: 8px;
    font-size: 12px;
  }

  .check-update-footer {
    min-height: 36px; // 进一步减少高度
    padding: 6px 16px;
  }
}
